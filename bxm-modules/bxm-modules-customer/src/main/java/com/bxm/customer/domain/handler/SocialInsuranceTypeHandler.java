package com.bxm.customer.domain.handler;

import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.bxm.customer.domain.vo.valueAdded.SocialInsuranceVO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import lombok.extern.slf4j.Slf4j;

/**
 * SocialInsuranceVO <-> JSON字符串 的MyBatis-Plus TypeHandler
 *
 * 继承 AbstractJsonTypeHandler 是MP提供的一种更便捷的方式来处理JSON
 * 使用FastJSON进行JSON序列化和反序列化处理
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Slf4j
@MappedTypes(SocialInsuranceVO.class)
@MappedJdbcTypes({JdbcType.VARCHAR, JdbcType.OTHER})
public class SocialInsuranceTypeHandler extends AbstractJsonTypeHandler<SocialInsuranceVO> {

    @Override
    protected SocialInsuranceVO parse(String json) {
        log.info("SocialInsuranceTypeHandler.parse() called with JSON: {}", json);
        try {
            if (json == null || json.trim().isEmpty()) {
                log.warn("Attempting to parse empty JSON string to SocialInsuranceVO");
                return null;
            }
            // 使用FastJSON解析JSON字符串为SocialInsuranceVO对象
            SocialInsuranceVO result = JSON.parseObject(json, SocialInsuranceVO.class);
            log.info("Successfully parsed JSON to SocialInsuranceVO: {}", result);
            return result;
        } catch (JSONException e) {
            log.error("Failed to deserialize JSON to SocialInsuranceVO: {}", json, e);
            throw new RuntimeException("Unable to deserialize JSON to SocialInsuranceVO: " + e.getMessage(), e);
        }
    }

    @Override
    protected String toJson(SocialInsuranceVO obj) {
        log.info("SocialInsuranceTypeHandler.toJson() called with object: {}", obj);
        try {
            if (obj == null) {
                log.info("Object is null, returning null");
                return null;
            }
            // 在序列化前验证业务逻辑
            if (!obj.isValid()) {
                String errorMsg = obj.getValidationMessage();
                log.error("Attempting to serialize invalid SocialInsuranceVO: {}", errorMsg);
                throw new RuntimeException("Social insurance validation failed, cannot serialize: " + errorMsg);
            }

            String jsonResult = JSON.toJSONString(obj);
            log.info("Successfully serialized SocialInsuranceVO to JSON: {}", jsonResult);
            return jsonResult;
        } catch (JSONException e) {
            log.error("Failed to serialize SocialInsuranceVO to JSON: {}", obj, e);
            throw new RuntimeException("Unable to serialize SocialInsuranceVO to JSON: " + e.getMessage(), e);
        }
    }
}
